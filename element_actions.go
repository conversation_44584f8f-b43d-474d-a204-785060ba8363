package main

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"math"
	"strings"
	"time"

	"github.com/playwright-community/playwright-go"
	"golang.org/x/image/draw"
)

// resizeImage 减小图片大小
// imageBytes: 原始图片字节数据
// maxWidth: 最大宽度，如果为0则保持原始宽度
// 返回: (压缩后的图片字节数据, 错误, 图片格式)
func resizeImage(imageBytes []byte, maxWidth int) ([]byte, string, error) {
	// 解码图片
	img, format, err := image.Decode(bytes.NewReader(imageBytes))
	if err != nil {
		return nil, "", fmt.Errorf("failed to decode image: %v", err)
	}

	// 获取原始尺寸
	bounds := img.Bounds()
	origWidth := bounds.Dx()
	origHeight := bounds.Dy()

	// 如果maxWidth为0或大于原始宽度，保持原始尺寸
	if maxWidth <= 0 || maxWidth >= origWidth {
		maxWidth = origWidth
	}

	// 计算新的高度，保持宽高比
	newHeight := int(math.Round(float64(origHeight) * float64(maxWidth) / float64(origWidth)))

	// 如果尺寸没有变化且质量为默认值，直接返回原图
	if maxWidth == origWidth {
		return imageBytes, format, nil
	}

	// 创建目标尺寸的图像
	dst := image.NewRGBA(image.Rect(0, 0, maxWidth, newHeight))

	// 使用CatmullRom算法进行高质量缩放
	draw.CatmullRom.Scale(dst, dst.Bounds(), img, bounds, draw.Over, nil)

	// 创建一个新的buffer
	buf := new(bytes.Buffer)

	// 根据原始图片格式进行编码
	format = strings.ToLower(format)
	if format == "jpeg" || format == "jpg" {
		// 使用JPEG编码，质量设为85（平衡质量和大小）
		err = jpeg.Encode(buf, dst, &jpeg.Options{Quality: 85})
	} else {
		// 默认使用PNG编码
		err = png.Encode(buf, dst)
		format = "png"
	}

	if err != nil {
		return nil, "", fmt.Errorf("failed to encode image: %v", err)
	}

	return buf.Bytes(), format, nil
}

// ScreenshotElement 抓取指定选择器的元素截图并转换为base64编码字符串
// selector: CSS选择器或文本选择器
// 返回: (base64编码的图片, 错误)
func (a *App) ScreenshotElement(selector string, maxWidth int) (string, error) {
	if a.page == nil {
		return "", fmt.Errorf("page not initialized")
	}
	// 查找元素
	locator := a.page.Locator(selector).First()
	if locator == nil {
		return "", fmt.Errorf("element not found: %s", selector)
	}
	// 检查元素是否可见
	visible, err := locator.IsVisible()
	if err != nil || !visible {
		return "", fmt.Errorf("element not visible: %s, error: %v", selector, err)
	}
	// 抓取元素截图，默认为PNG格式
	screenshotBytes, err := locator.Screenshot()
	if err != nil {
		return "", fmt.Errorf("failed to take screenshot: %v", err)
	}
	// 压缩图片
	var processedBytes []byte
	var format string
	compressedBytes, format, err := resizeImage(screenshotBytes, maxWidth)
	if err != nil {
		fmt.Printf("压缩图片失败: %v, 使用原图\n", err)
		processedBytes = screenshotBytes
		format = "png" // 默认为PNG格式
	} else {
		processedBytes = compressedBytes
	}
	// 将图像转换为base64编码
	imageBase64 := base64.StdEncoding.EncodeToString(processedBytes)
	// 添加data URL前缀，使用正确的MIME类型
	var dataURL string
	if format == "jpeg" || format == "jpg" {
		dataURL = "data:image/jpeg;base64," + imageBase64
	} else {
		dataURL = "data:image/png;base64," + imageBase64
	}

	return dataURL, nil
}

// ClickElement 点击指定选择器的元素
// selector: CSS选择器或文本选择器
// 返回: 操作过程中遇到的错误
func (a *App) ClickElement(selector string) error {
	if a.page == nil {
		return fmt.Errorf("page not initialized")
	}

	// 查找元素
	locator := a.page.Locator(selector).First()
	if locator == nil {
		return fmt.Errorf("element not found: %s", selector)
	}

	// 检查元素是否可见
	visible, err := locator.IsVisible()
	if err != nil || !visible {
		return fmt.Errorf("element not visible: %s, error: %v", selector, err)
	}

	// 点击元素
	if err := locator.Click(); err != nil {
		return fmt.Errorf("failed to click element: %v", err)
	}

	if AppLogger != nil {
		AppLogger.Debug("已点击元素: %s", selector)
	}

	return nil
}

// FillElement 填充指定选择器的元素
// selector: CSS选择器或文本选择器
// value: 要填充的值
// 返回: 操作过程中遇到的错误
func (a *App) FillElement(selector string, value string) error {
	if a.page == nil {
		return fmt.Errorf("page not initialized")
	}

	// 查找元素
	locator := a.page.Locator(selector).First()
	if locator == nil {
		return fmt.Errorf("element not found: %s", selector)
	}

	// 检查元素是否可见
	visible, err := locator.IsVisible()
	if err != nil || !visible {
		return fmt.Errorf("element not visible: %s, error: %v", selector, err)
	}

	// 填充元素
	if err := locator.Fill(value); err != nil {
		return fmt.Errorf("failed to fill element: %v", err)
	}

	if AppLogger != nil {
		AppLogger.Debug("已填充元素 %s 的值为: %s", selector, value)
	}

	return nil
}

// ScreenshotArea 截取页面指定区域的截图并转换为base64编码字符串
// x, y: 截图区域的左上角坐标
// width, height: 截图区域的宽度和高度
// maxWidth: 最大宽度，如果为0则保持原始宽度
// 返回: (base64编码的图片, 错误)
func (a *App) ScreenshotArea(x, y, width, height, maxWidth int) (string, error) {
	if a.page == nil {
		return "", fmt.Errorf("page not initialized")
	}
	// 确保参数有效
	if width <= 0 || height <= 0 {
		return "", fmt.Errorf("invalid dimensions: width and height must be positive")
	}

	if x < 0 || y < 0 {
		return "", fmt.Errorf("invalid coordinates: x and y must be non-negative")
	}
	// 创建截图选项
	options := playwright.PageScreenshotOptions{
		Clip: &playwright.Rect{
			X:      float64(x),
			Y:      float64(y),
			Width:  float64(width),
			Height: float64(height),
		},
	}
	// 使用Playwright的Screenshot方法截取指定区域
	screenshotBytes, err := a.page.Screenshot(options)
	if err != nil {
		return "", fmt.Errorf("failed to take screenshot: %v", err)
	}

	if AppLogger != nil {
		AppLogger.Debug("已截取区域截图: x=%d, y=%d, width=%d, height=%d", x, y, width, height)
	}
	// 压缩图片
	var processedBytes []byte
	var format string
	compressedBytes, format, err := resizeImage(screenshotBytes, maxWidth)
	if err != nil {
		if AppLogger != nil {
			AppLogger.Warning("压缩图片失败: %v, 使用原图", err)
		}
		processedBytes = screenshotBytes
		format = "png" // 默认为PNG格式
	} else {
		processedBytes = compressedBytes
	}
	// 将图像转换为base64编码
	imageBase64 := base64.StdEncoding.EncodeToString(processedBytes)
	// 添加data URL前缀，使用正确的MIME类型
	var dataURL string
	if format == "jpeg" || format == "jpg" {
		dataURL = "data:image/jpeg;base64," + imageBase64
	} else {
		dataURL = "data:image/png;base64," + imageBase64
	}

	return dataURL, nil
}

// CaptureFullPage 截取整个页面的截图并转换为base64编码字符串
// 返回: (base64编码的图片, 错误)
func (a *App) CaptureFullPage() (string, error) {
	if a.page == nil {
		return "", fmt.Errorf("page not initialized")
	}

	// 使用Playwright的Screenshot方法截取整个页面
	options := playwright.PageScreenshotOptions{
		FullPage: playwright.Bool(true),
	}
	screenshotBytes, err := a.page.Screenshot(options)
	if err != nil {
		return "", fmt.Errorf("failed to take full page screenshot: %v", err)
	}

	if AppLogger != nil {
		AppLogger.Debug("已截取整个页面截图")
	}

	// 压缩图片，最大宽度设为1200，保持较好的清晰度
	var processedBytes []byte
	var format string
	compressedBytes, format, err := resizeImage(screenshotBytes, 1200)
	if err != nil {
		if AppLogger != nil {
			AppLogger.Warning("压缩图片失败: %v, 使用原图", err)
		}
		processedBytes = screenshotBytes
		format = "png" // 默认为PNG格式
	} else {
		processedBytes = compressedBytes
	}

	// 将图像转换为base64编码
	imageBase64 := base64.StdEncoding.EncodeToString(processedBytes)

	// 添加data URL前缀，使用正确的MIME类型
	var dataURL string
	if format == "jpeg" || format == "jpg" {
		dataURL = "data:image/jpeg;base64," + imageBase64
	} else {
		dataURL = "data:image/png;base64," + imageBase64
	}

	return dataURL, nil
}

// 元素操作信息
type ElementOperation struct {
	Selector    string `json:"selector"`
	Description string `json:"description"`
	Type        string `json:"type"` // "click" 或 "input"
	Value       string `json:"value,omitempty"`
}

// ExecuteElementOperations 执行一系列元素操作
// operations: 要执行的操作列表
// 返回: 操作过程中遇到的错误
func (a *App) ExecuteElementOperations(operationsJson string) error {
	if a.page == nil {
		return fmt.Errorf("page not initialized")
	}

	// 解析操作列表
	var operations []ElementOperation
	if err := json.Unmarshal([]byte(operationsJson), &operations); err != nil {
		return fmt.Errorf("failed to parse operations: %v", err)
	}

	if len(operations) == 0 {
		return fmt.Errorf("no operations to execute")
	}

	if AppLogger != nil {
		AppLogger.Info("开始执行元素操作，共 %d 个操作", len(operations))
	}

	// 执行每个操作
	for i, op := range operations {
		if AppLogger != nil {
			AppLogger.Debug("执行操作 %d/%d: %s (%s)", i+1, len(operations), op.Description, op.Type)
		}

		// 根据操作类型执行不同的操作
		switch op.Type {
		case "click":
			if err := a.ClickElement(op.Selector); err != nil {
				return fmt.Errorf("failed to click element '%s': %v", op.Description, err)
			}
			// 点击后等待一小段时间，让页面响应
			time.Sleep(500 * time.Millisecond)
		case "input":
			if err := a.FillElement(op.Selector, op.Value); err != nil {
				return fmt.Errorf("failed to fill element '%s': %v", op.Description, err)
			}
			// 输入后等待一小段时间
			time.Sleep(300 * time.Millisecond)
		default:
			return fmt.Errorf("unknown operation type: %s", op.Type)
		}
	}

	if AppLogger != nil {
		AppLogger.Info("元素操作执行完成")
	}

	return nil
}

// SelectElement 让用户选择网页元素并返回选择器
// 返回: (CSS选择器, 错误)
func (a *App) SelectElement() (string, error) {
	if a.page == nil {
		return "", fmt.Errorf("page not initialized")
	}

	if AppLogger != nil {
		AppLogger.Debug("开始选择网页元素")
	}

	// 创建一个通道，用于接收选择结果
	resultChan := make(chan string, 1)
	errorChan := make(chan error, 1)

	// 在页面上注入选择元素的JavaScript代码
	js := `
	() => {
		return new Promise((resolve, reject) => {
			// 添加提示信息
			const overlay = document.createElement('div');
			overlay.style.position = 'fixed';
			overlay.style.top = '0';
			overlay.style.left = '0';
			overlay.style.width = '100%';
			overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
			overlay.style.color = 'white';
			overlay.style.padding = '10px';
			overlay.style.zIndex = '9999';
			overlay.style.textAlign = 'center';
			overlay.textContent = '请点击要选择的元素，按ESC取消';
			document.body.appendChild(overlay);

			// 高亮鼠标悬停的元素
			let highlightElement = null;
			const highlight = (element) => {
				if (highlightElement) {
					highlightElement.style.outline = '';
				}
				highlightElement = element;
				if (highlightElement) {
					highlightElement.style.outline = '2px solid red';
				}
			};

			// 鼠标移动事件
			const mousemoveHandler = (e) => {
				highlight(e.target);
				e.stopPropagation();
			};

			// 点击事件
			const clickHandler = (e) => {
				e.preventDefault();
				e.stopPropagation();

				// 移除事件监听器和高亮
				document.removeEventListener('mousemove', mousemoveHandler, true);
				document.removeEventListener('click', clickHandler, true);
				document.removeEventListener('keydown', keydownHandler, true);
				if (highlightElement) {
					highlightElement.style.outline = '';
				}
				document.body.removeChild(overlay);

				// 生成选择器
				const element = e.target;
				let selector = '';

				// 尝试使用ID
				if (element.id) {
					selector = '#' + element.id;
				}
				// 尝试使用类名
				else if (element.className && typeof element.className === 'string') {
					const classes = element.className.split(' ').filter(c => c);
					if (classes.length > 0) {
						selector = '.' + classes.join('.');
					}
				}

				// 如果没有ID或类名，使用标签名和索引
				if (!selector) {
					const tagName = element.tagName.toLowerCase();
					const siblings = Array.from(element.parentNode.children).filter(el => el.tagName.toLowerCase() === tagName);
					const index = siblings.indexOf(element);
					selector = tagName + ':nth-child(' + (index + 1) + ')';
				}

				// 如果元素有name属性，使用它
				if (element.name) {
					selector = '[name="' + element.name + '"]';
				}

				// 如果是输入框，添加更具体的选择器
				if (element.tagName.toLowerCase() === 'input') {
					const type = element.type || 'text';
					selector = 'input[type="' + type + '"]' + (selector.startsWith('[') ? selector : '');
				}

				resolve(selector);
			};

			// 键盘事件，按ESC取消
			const keydownHandler = (e) => {
				if (e.key === 'Escape') {
					document.removeEventListener('mousemove', mousemoveHandler, true);
					document.removeEventListener('click', clickHandler, true);
					document.removeEventListener('keydown', keydownHandler, true);
					if (highlightElement) {
						highlightElement.style.outline = '';
					}
					document.body.removeChild(overlay);
					resolve('');
				}
			};

			// 添加事件监听器
			document.addEventListener('mousemove', mousemoveHandler, true);
			document.addEventListener('click', clickHandler, true);
			document.addEventListener('keydown', keydownHandler, true);
		});
	}
	`

	// 执行JavaScript代码
	go func() {
		result, err := a.page.Evaluate(js)
		if err != nil {
			errorChan <- fmt.Errorf("选择元素失败: %v", err)
			return
		}

		if result == nil {
			resultChan <- ""
			return
		}

		selector, ok := result.(string)
		if !ok {
			errorChan <- fmt.Errorf("无效的选择器类型")
			return
		}

		resultChan <- selector
	}()

	// 等待结果或错误
	select {
	case selector := <-resultChan:
		if AppLogger != nil && selector != "" {
			AppLogger.Debug("已选择元素，选择器: %s", selector)
		}
		return selector, nil
	case err := <-errorChan:
		return "", err
	case <-time.After(60 * time.Second): // 设置超时时间
		return "", fmt.Errorf("选择元素超时")
	}
}
