package main

import (
	"fmt"
	"os"
	"path/filepath"
	"testing"

	"github.com/adrg/xdg"
	"github.com/glebarez/sqlite"
	"gorm.io/gorm"
)

// 简化版的 GetDBPath 函数，仅用于测试
func testGetDBPath() string {
	// 使用不同的数据库文件名，避免与实际应用冲突
	return filepath.Join(xdg.ConfigHome, "ai-grading", "test-grading.db")
}

func TestSQLite(t *testing.T) {
	// 确保数据库目录存在
	dbDir := filepath.Dir(testGetDBPath())
	if err := os.MkdirAll(dbDir, 0o755); err != nil {
		t.Fatalf("无法创建数据库目录: %v", err)
	}

	// 打开数据库连接
	db, err := gorm.Open(sqlite.Open(testGetDBPath()), &gorm.Config{})
	if err != nil {
		t.Fatalf("无法打开数据库: %v", err)
	}

	// 测试数据库连接
	sqlDB, err := db.DB()
	if err != nil {
		t.Fatalf("获取数据库连接失败: %v", err)
	}

	// 测试连接是否正常
	if err := sqlDB.Ping(); err != nil {
		t.Fatalf("数据库连接测试失败: %v", err)
	}

	fmt.Println("数据库连接测试成功！")
	fmt.Printf("数据库路径: %s\n", testGetDBPath())

	// 创建一个简单的测试表
	type TestTable struct {
		ID      string `gorm:"primaryKey"`
		Content string
	}

	// 自动迁移表结构
	if err := db.AutoMigrate(&TestTable{}); err != nil {
		t.Fatalf("无法迁移表结构: %v", err)
	}

	// 测试创建记录
	testRecord := TestTable{
		ID:      "test-id",
		Content: "测试内容",
	}

	if err := db.Create(&testRecord).Error; err != nil {
		t.Fatalf("创建记录失败: %v", err)
	}

	fmt.Println("创建记录成功！")

	// 测试查询记录
	var result TestTable
	if err := db.First(&result, "id = ?", "test-id").Error; err != nil {
		t.Fatalf("查询记录失败: %v", err)
	}

	fmt.Printf("查询记录成功: %+v\n", result)

	// 测试删除记录
	if err := db.Delete(&result).Error; err != nil {
		t.Fatalf("删除记录失败: %v", err)
	}

	fmt.Println("删除记录成功！")

	// 删除测试数据库文件
	sqlDB.Close()
	if err := os.Remove(testGetDBPath()); err != nil {
		t.Logf("删除测试数据库文件失败: %v", err)
	}

	fmt.Println("数据库测试全部通过！")
}
