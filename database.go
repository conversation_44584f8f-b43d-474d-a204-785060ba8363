package main

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/glebarez/sqlite" // 纯Go实现的SQLite驱动，无需CGO
	"gorm.io/gorm"
)

// DB 全局数据库连接
var (
	DB   *gorm.DB
	dbMu sync.Mutex
)

// GradingCriteria 表示评分标准
type GradingCriteria struct {
	ID        string `gorm:"primaryKey" json:"id"`    // 哈希值作为主键
	Content   string `gorm:"not null" json:"content"` // 评分标准内容
	CreatedAt time.Time
	UpdatedAt time.Time
}

// GradingRecord 表示阅卷记录
type GradingRecord struct {
	gorm.Model
	UserEmail         string          `gorm:"not null" json:"user_email"`
	AnswerImage       string          `gorm:"not null" json:"answer_image"`              // Base64编码的图片
	AnswerImageHash   string          `gorm:"not null;index" json:"answer_image_hash"`   // 图片哈希值，用于唯一标识
	GradingCriteriaID string          `gorm:"not null;index" json:"grading_criteria_id"` // 评分标准ID（外键）
	GradingCriteria   GradingCriteria `gorm:"foreignKey:GradingCriteriaID" json:"-"`     // 关联的评分标准
	AnswerText        string          `gorm:"not null" json:"answer_text"`               // 学生答案文本
	Score             float64         `gorm:"not null" json:"score"`                     // 得分
	ScoreDetails      string          `gorm:"not null" json:"score_details"`             // 得分细节
}

// InitDB 初始化GORM数据库连接
func InitDB() (*gorm.DB, error) {
	// 使用互斥锁确保线程安全
	dbMu.Lock()
	defer dbMu.Unlock()

	// 如果全局DB已经初始化，直接返回
	if DB != nil {
		return DB, nil
	}

	// 确保数据库目录存在
	dbDir := filepath.Dir(GetDBPath())
	if err := os.MkdirAll(dbDir, 0o755); err != nil {
		return nil, fmt.Errorf("无法创建数据库目录: %v", err)
	}

	// 打开数据库连接
	db, err := gorm.Open(sqlite.Open(GetDBPath()), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("无法打开数据库: %v", err)
	}

	// 自动迁移表结构
	if err := db.AutoMigrate(&GradingCriteria{}, &GradingRecord{}); err != nil {
		return nil, fmt.Errorf("无法迁移表结构: %v", err)
	}

	// 保存到全局变量
	DB = db

	return DB, nil
}

// GetDB 获取全局数据库连接
func GetDB() *gorm.DB {
	// 使用互斥锁确保线程安全
	dbMu.Lock()
	defer dbMu.Unlock()

	// 如果全局DB未初始化，尝试初始化
	if DB == nil {
		db, err := InitDB()
		if err != nil {
			// 在实际应用中应该处理这个错误，这里简单返回空
			fmt.Printf("无法初始化数据库: %v\n", err)
			return nil
		}
		return db
	}

	return DB
}

// GenerateCriteriaHash 从评分标准内容生成MD5哈希值
// 返回一个32字符的十六进制字符串
func GenerateCriteriaHash(content string) string {
	// 计算MD5哈希值
	hash := md5.Sum([]byte(content))

	// 将哈希值转换为十六进制字符串
	return hex.EncodeToString(hash[:])
}

// FindOrCreateGradingCriteria 查找或创建评分标准
// 参数:
// - db: 数据库连接
// - content: 评分标准内容
// 返回:
// - 评分标准ID
// - 错误信息
func FindOrCreateGradingCriteria(db *gorm.DB, content string) (string, error) {
	// 生成评分标准哈希值
	criteriaID := GenerateCriteriaHash(content)

	// 检查是否已存在相同哈希值的评分标准
	var existingCriteria GradingCriteria
	result := db.Where("id = ?", criteriaID).First(&existingCriteria)

	// 如果已存在相同哈希值的评分标准，则直接返回ID
	if result.Error == nil {
		return criteriaID, nil
	}

	// 创建新的评分标准
	criteria := GradingCriteria{
		ID:      criteriaID,
		Content: content,
	}

	// 保存到数据库
	if err := db.Create(&criteria).Error; err != nil {
		return "", fmt.Errorf("保存评分标准失败: %v", err)
	}

	return criteriaID, nil
}
