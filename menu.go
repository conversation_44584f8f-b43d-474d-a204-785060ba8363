package main

import (
	"log"
	"os"

	"github.com/wailsapp/wails/v2/pkg/menu"
	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// CreateAppMenu 创建应用菜单（未登录状态）
func CreateAppMenu(app *App) *menu.Menu {
	// 创建基本菜单（未登录状态）
	return createBasicMenu(app)
}

// createBasicMenu 创建基本菜单（未登录状态）
func createBasicMenu(app *App) *menu.Menu {
	appMenu := menu.NewMenu()

	// 文件菜单
	fileMenu := appMenu.AddSubmenu("文件")
	fileMenu.AddText("退出", nil, func(_ *menu.CallbackData) {
		os.Exit(0)
	})

	// 工具菜单
	toolsMenu := appMenu.AddSubmenu("工具")
	toolsMenu.AddText("安装必要环境", nil, func(_ *menu.CallbackData) {
		go func() {
			if err := app.InstallPlaywright(); err != nil {
				log.Println("could not install playwright:", err)
			}
		}()
	})
	toolsMenu.AddSeparator()
	toolsMenu.AddText("清除应用数据", nil, func(_ *menu.CallbackData) {
		app.ShowClearDataDialog()
	})

	// 帮助菜单
	helpMenu := appMenu.AddSubmenu("帮助")
	helpMenu.AddText("关于", nil, func(_ *menu.CallbackData) {
		app.ShowAbout()
	})

	return appMenu
}

// createFullMenu 创建完整菜单（登录状态）
func createFullMenu(app *App) *menu.Menu {
	appMenu := menu.NewMenu()

	// 文件菜单
	fileMenu := appMenu.AddSubmenu("文件")
	fileMenu.AddText("退出", nil, func(_ *menu.CallbackData) {
		os.Exit(0)
	})

	// 工具菜单
	toolsMenu := appMenu.AddSubmenu("工具")
	toolsMenu.AddText("安装必要环境", nil, func(_ *menu.CallbackData) {
		go func() {
			if err := app.InstallPlaywright(); err != nil {
				log.Println("could not install playwright:", err)
			}
		}()
	})
	toolsMenu.AddSeparator()
	toolsMenu.AddText("阅卷记录", nil, func(_ *menu.CallbackData) {
		app.ShowGradingRecords()
	})
	toolsMenu.AddText("清除应用数据", nil, func(_ *menu.CallbackData) {
		app.ShowClearDataDialog()
	})
	toolsMenu.AddSeparator()
	// 导出子菜单
	exportMenu := toolsMenu.AddSubmenu("导出")
	// exportMenu.AddText("阅卷记录导出为Word", nil, func(_ *menu.CallbackData) {
	// 	go func() {
	// 		if err := app.ExportGradingRecordsToWord(""); err != nil {
	// 			log.Println("导出阅卷记录失败:", err)
	// 		}
	// 	}()
	// })
	exportMenu.AddText("阅卷记录导出为Excel", nil, func(_ *menu.CallbackData) {
		go func() {
			if err := app.ExportGradingRecordsToExcel(""); err != nil {
				log.Println("导出阅卷记录失败:", err)
			}
		}()
	})
	exportMenu.AddText("阅卷记录导出为CSV", nil, func(_ *menu.CallbackData) {
		go func() {
			if err := app.ExportGradingRecordsToCSV(""); err != nil {
				log.Println("导出阅卷记录失败:", err)
			}
		}()
	})
	// 充值菜单
	rechargeMenu := appMenu.AddSubmenu("充值")
	rechargeMenu.AddText("通过支付宝", nil, func(_ *menu.CallbackData) {
		app.ShowRechargeDialog()
	})
	// 帮助菜单
	helpMenu := appMenu.AddSubmenu("帮助")
	helpMenu.AddText("关于", nil, func(_ *menu.CallbackData) {
		app.ShowAbout()
	})

	return appMenu
}

// UpdateAppMenu 根据登录状态更新应用菜单
func UpdateAppMenu(app *App, isLoggedIn bool) *menu.Menu {
	var newMenu *menu.Menu
	if isLoggedIn {
		newMenu = createFullMenu(app)
	} else {
		newMenu = createBasicMenu(app)
	}

	// 如果应用上下文已初始化，则更新菜单
	if app.ctx != nil {
		runtime.MenuSetApplicationMenu(app.ctx, newMenu)
	}

	return newMenu
}
