# 山竹阅卷使用说明书

## 软件介绍

山竹阅卷是一款基于人工智能技术的自动评分工具，专为教师和教育工作者设计，能够快速、准确地对学生答案进行评分。系统支持多种科目的评分标准设置，可以大幅提高阅卷效率，减轻教师工作负担。

---

## 安装要求

### 系统要求

- 操作系统：Windows10/11、macOS（若有需求可以发布）
- 网络连接：稳定的互联网连接

### 安装步骤

1. 下载最新版本的安装包
2. 运行安装程序，按照提示完成安装
3. 首次运行时，需要登录账号（如未注册，请先注册账号）
4. 登录成功后，**如果是第一次使用，首先要安装环境**，工具菜单中点击「安装必要环境」按钮，耐心等待安装完成，安装时间取决于网络速度
5. 完成环境安装后，即可开始使用

---

## 主要功能

### 1. 自动阅卷

系统能够根据预设的评分标准，自动识别学生答案内容，并给出相应的分数和评分详情。

### 2. 评分标准设置

系统支持自定义评分标准，可以根据不同科目和题型设置详细的得分点和扣分点。同一题评分标准会自动保存，下次使用时无需重新输入。

### 3. 阅卷记录管理

- **记录保存**：自动保存所有阅卷记录
- **记录查询**：支持按时间、题目等条件查询历史记录
- **数据导出**：支持将阅卷记录导出为 CSV和Excel 等格式，方便分享和存档

### 4. 用户管理

- **登录**：使用邮箱账号密码登录系统
- **注册**：首次使用需要注册账号，根据提示填写账号密码等信息，系统会发送验证码到邮箱，请根据提示完成验证
- **忘记密码**：点击「忘记密码」按钮，系统会发送重置密码令牌到注册邮箱，根据提示重置密码
- **账户积分余额**：系统会显示账户积分余额，如积分余额不足，使用工具菜单中充值功能充值

### 5. 计费

系统采用积分制，用户可以通过充值、积分兑换等方式获取积分。

积分兑换比例: **1元RMB=1000积分**

根据科目、题型、题目内容等因素，系统会计算出消耗的积分。根据统计，每题约消耗 10～20 积分。

用户可以通过以下方式获取积分：

- **充值**：
  - 点击「工具」菜单-「充值」
  - 在弹出的对话框中输入充值金额
  - 点击「充值」按钮
  - 系统后台会进行审核，通过审核通过后，系统会向用户发送积分

- **积分兑换**：
  - _有待推出_

### 6. 界面功能

- **窗口置顶**：可设置应用窗口始终置顶，方便操作
- **窗口缩放**：可设置窗口缩放比例，方便查看阅卷记录更多信息

### 7. 阅卷平台支持

**通用平台**：支持对任意网页内容进行抓图和评分

**平台支持**：支持对以下平台进行自动化操作

- **懂你 100 平台**
- **光大阅卷平台**
- **智学网平台**

欢迎提出需求或建议，我们将尽快改进和完善平台功能。
假如没有适配您使用的平台，请参考[通用阅卷](#通用阅卷)章节进行适配。

---

## 操作流程

总体操作流程如下：

_**为了保证自动阅卷的质量，首先利用测试阅卷验证评分标准质量，我们建议您按照以下步骤进行操作：**_

1. **导航到阅卷页面**
2. **获取学生答案**
3. **选择科目并设置评分标准**
4. **点击测试阅卷进行评分**
5. **调整分数（可选）**
6. **提交评分结果**
7. **根据评分结果对评分标准进行调整，重复 2-6 步骤，直到评分结果满意，则停止测试阅卷**
8. **设置阅卷数量（建议每题自动阅卷初次设置数量为个位数，看看效果）**
9. **AI 自动阅卷**：系统会自动进行批量阅卷，并记录结果

### 测试阅卷流程

**基本阅卷流程图：**

```mermaid
flowchart TD
    A[开始] --> B[导航到阅卷页面]
    B --> C[获取学生答案]
    C --> D[选择科目并设置评分标准]
    D --> E[点击测试阅卷进行评分]
    E --> F[调整分数<可选>]
    F --> G[提交评分结果]
    G --> H{评分结果满意?}
    H -->|否| I[调整评分标准]
    I --> C
    H -->|是| J[设置阅卷数量]
    J --> K[AI自动阅卷]
    K --> L[完成]
```

#### 1. **导航到阅卷页面**

- 在阅卷平台选择器中选择需要使用的平台（如懂你 100 平台、光大阅卷平台、智学网平台等）
- 点击「打开」按钮，系统会自动打开一个浏览器窗口
- 在打开的浏览器中，使用您的平台账号登录
- 登录成功后，导航至阅卷页面
- **注意事项**：在整个过程中，请勿改变浏览器标签页或关闭浏览器窗口，否则需要重新按照流程操作

![1747892905064](image/USAGE/1747892905064.png)

```mermaid
flowchart LR
  A[开始] --> B[选择阅卷平台]
  B --> C[点击「打开」按钮]
  C --> D[浏览器自动打开]
  D --> E[用户登录平台账号]
  E --> F[导航至阅卷页面]

  subgraph 注意事项
  G[不要改变浏览器标签页]
  H[不要关闭浏览器窗口]
  end

  F -.-> 注意事项
```

#### 2. **获取学生答案**

- 点击「抓图」按钮
- 系统会自动识别图片
- 识别成功后，会自动将识别结果显示在答题卡区域
- 点击图片可预览，检查识别结果是否正确

![1747895821882](image/USAGE/1747895821882.png)

#### 3. **设置评分标准**

- 首先选择科目
- 点击「编辑」按钮
- 在弹出的对话框中输入或粘贴评分标准（包含得分点和不得分点）
- 点击「保存」按钮完成设置

![1747894759893](image/USAGE/1747894759893.png)

```mermaid
flowchart LR
  A[开始] --> B[选择科目]
  B --> C[点击「编辑」按钮]
  C --> D[在对话框中输入评分标准]
  D --> D1[包含得分点和不得分点]
  D1 --> E[点击「保存」按钮]
```

#### 4. **进行评分**

- 点击「测试阅卷」按钮
- 系统会根据评分标准对学生答案进行分析
- 评分结果会显示在界面上，包括得分和详细的评分说明

#### 5. **调整分数（可选）**

- 如需调整分数，点击分数旁边的「修改分数」按钮
- 输入新的分数
- 如果想重置分数为 AI 评分结果，点击分数旁边的「还原」按钮

#### 6. **提交评分结果**

- 点击「提交评分」按钮
- 执行阅卷平台提交操作

### 自动阅卷流程

_**建议先利用[测试阅卷](#测试阅卷流程)验证评分标准质量和流程是否正确**_
_**如果测试阅卷结果满意，才进行自动阅卷**_
**自动阅卷流程：**

1. 选择阅卷平台（自定义请看[通用阅卷](#通用阅卷)）
2. 登录平台账号并导航至阅卷页面
3. 设置科目
4. 设置评分标准
5. 设置需要阅卷的试卷数量（_建议每题自动阅卷初次设置数量为个位数，看看效果_）
6. 点击「AI 自动阅卷」按钮

**自动阅卷流程图：**

```mermaid
flowchart TD
    A[开始] --> B1[选择阅卷平台]
    B1 --> B2[登录平台账号]
    B2 --> B3[导航至阅卷页面]
  
    B3 --> C[设置自动阅卷]
    C --> C1[选择科目]
    C1 --> C2[设置评分标准]
    C2 --> C3[设置阅卷数量]
  
    C3 --> D{是否为自定义URL?}
    D -->|是| E[完成区域选择]
    E --> F[完成元素选择]
    D -->|否| G[开始自动阅卷]
    F --> G
  
    G --> H[系统自动批量阅卷]
    H --> I[记录阅卷结果]
    I --> J[完成]
  
    subgraph 注意事项
    K[不要改变浏览器标签页]
    L[不要关闭浏览器窗口]
    M[建议初次设置少量试卷测试效果]
    end
  
    B3 -.-> 注意事项
    G -.-> 注意事项
```

## 通用阅卷

_**如果平台不支持，可使用通用阅卷功能**_
**通用阅卷流程：**

1. 阅卷平台选择「自定义」
2. 点击「打开」按钮
3. 在浏览器输入需要阅卷的URL
4. 导航至阅卷页面
5. 点击「区域选择」按钮
6. 在选择截图区域对话框，在图片上按住鼠标左键并拖动来选择区域
7. 点击「截取选中区域」按钮
8. 点击「抓图」按钮，在答题卡区域会显示识别结果
9. 验证识别结果是否正确
10. 点击「元素选择」按钮，作用是模拟人工点击操作，用户提交评分结果
11. 请添加一个输入操作元素（用于填写评分结果）和若干点击操作元素。参考示例[示例](#选择元素操作)
12. 设置科目
13. 设置评分标准

### 选择元素操作

//TODO: 添加动图

示例，假如分数输入框已在页面上：

1. 点击「元素选择」按钮
2. 在弹出的对话框中，点击「输入操作」按钮
3. 点击「选择元素」按钮，切换到浏览器阅卷页面，选中分数输入框
4. 返回软件对话框，确认当前选择的元素，并点击「添加到列表」按钮
5. 然后添加下一步操作，点击「点击操作」按钮，切换到浏览器阅卷页面，选中提交按钮
6. 返回软件对话框，确认当前选择的元素，并点击「添加到列表」按钮
7. 点击「保存」按钮完成元素选择

## 数据管理

### 阅卷记录查看

1. 点击「阅卷记录」按钮
2. 在弹出的对话框中可以查看所有历史记录
3. 可以按时间、科目等条件筛选记录

### 数据导出

1. 点击「工具」菜单-「阅卷记录」
2. 在阅卷记录对话框中，选择需要导出记录的时间范围和评分标准（题目按评分标准分类）
3. 点击「导出Excel」或者「导出CSV」按钮
4. 选择保存位置
5. 系统会自动生成文件并保存到指定位置

### 数据清理

1. 点击「工具」菜单-「清理应用数据」
2. 选择需要清理的数据类型
3. 确认清理操作

## 常见问题解答

### 1. 评分结果不准确怎么办？

- 检查评分标准是否清晰明确
- 确保学生答案图片清晰可辨
- 使用手动调整功能修正分数

### 2. 自动阅卷无法启动？

- 检查是否已设置评分标准
- 对于自定义 URL，确保已完成区域选择和元素选择
- 检查错误提示信息，根据提示解决问题

### 3. 账户余额不足？

- 系统会显示「账户余额不足」的提示
- 使用工具菜单-「充值」进行充值

### 4. 应用数据位置？

- Windows: C:\Users\<USER>\AppData\Local\ai-grading
- macOS: ~/Library/Application Support/ai-grading

## 版本更新说明

### 2025.04.18 版本

1. 新增光大阅卷平台支持
2. 新增应用数据清理功能

**注意**：升级到此版本后，需要删除阅卷记录数据库（Windows 目录位置：C:\Users\<USER>\AppData\Local\ai-grading）

## 技术支持

如遇到使用问题，请联系技术支持团队获取帮助。
