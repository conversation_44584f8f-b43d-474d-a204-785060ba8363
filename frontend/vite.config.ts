import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // React 核心相关
          'react-core': ['react', 'react-dom', 'react-router-dom'],
          
          // Antd 组件按功能分组
          'antd-base': ['antd/es/config-provider', 'antd/es/theme'],
          'antd-layout': ['antd/es/layout', 'antd/es/grid', 'antd/es/space'],
          'antd-inputs': ['antd/es/input', 'antd/es/form', 'antd/es/button'],
          'antd-feedback': ['antd/es/message', 'antd/es/notification'],
          
          // Icons 单独打包
          'icons': ['@ant-design/icons'],
        }
      }
    },
    chunkSizeWarningLimit: 500
  }
})
