import React, { create<PERSON>ontext, useState, useContext, ReactNode, useCallback, useEffect } from 'react';
import { LoginAPI, RegisterAPI, GetBalance, RecoverPasswordAPI, VerifyTokenAPI, VerifySignupTokenAPI, UpdatePasswordAPI } from '../../wailsjs/go/main/APIService';
import { SaveAppAuth, GetAppAuth } from '../../wailsjs/go/main/App';
import { LogError, LogInfo } from '../../wailsjs/runtime/runtime';

// 为window.go添加类型声明
declare global {
  interface Window {
    go: {
      main: {
        App: {
          UpdateMenuAfterLogin: () => Promise<void>;
          UpdateMenuAfterLogout: () => Promise<void>;
        };
      };
    };
  }
}

// 验证令牌响应接口
interface VerifyTokenResponseType {
  success: boolean;
  message: string;
  access_token: string;
  refresh_token: string;
  expires_in: number;
  expires_at: number;
}

interface AuthContextType {
  isAuthenticated: boolean;
  username: string | null;
  balance: number | null; // 用户积分
  isBalanceSufficient: boolean;
  login: (username: string, password: string, remember?: boolean) => Promise<void>;
  register: (email: string, password: string) => Promise<void>;
  logout: () => void;
  getSavedCredentials: () => Promise<{ username: string; password: string } | null>;
  refreshBalance: () => Promise<void>;
  updateBalance: (newBalance: number) => void;
  recoverPassword: (email: string) => Promise<void>;
  verifyToken: (email: string, token: string) => Promise<VerifyTokenResponseType>;
  verifySignupToken: (email: string, token: string) => Promise<VerifyTokenResponseType>;
  updatePassword: (password: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [username, setUsername] = useState<string | null>(null);
  const [balance, setBalance] = useState<number | null>(null);
  const [isBalanceSufficient, setIsBalanceSufficient] = useState<boolean>(true);

  // 更新积分并检查是否足够
  const updateBalance = useCallback((newBalance: number) => {
    setBalance(newBalance);
    setIsBalanceSufficient(newBalance >= 100);
    LogInfo(`更新积分: ${newBalance}, 是否足够: ${newBalance >= 100}`);
  }, []);

  // 刷新余额的函数
  const refreshBalance = useCallback(async () => {
    if (!isAuthenticated) return;

    try {
      const balance = await GetBalance();
      updateBalance(balance);
      LogInfo(`获取积分成功: ${balance}`);
    } catch (error) {
      LogError(`获取余额失败: ${error}`);
    }
  }, [isAuthenticated, updateBalance]);

  // 登录成功后自动获取余额
  useEffect(() => {
    if (isAuthenticated) {
      refreshBalance();
    }
  }, [isAuthenticated, refreshBalance]);

  const login = useCallback(async (username: string, password: string, remember: boolean = false) => {
    try {
      await LoginAPI(username, password);
      setIsAuthenticated(true);
      setUsername(username);

      // Always save username to configuration file
      // Only save password if remember is checked
      try {
        if (remember) {
          // Save both username and password
          await SaveAppAuth(`${username}`, `${password}`);
        } else {
          // Save only username with empty password
          await SaveAppAuth(`${username}`, "");
        }
      } catch (saveError) {
        LogError(`Failed to save credentials to config: ${saveError}`);
        // Continue with login even if saving to config fails
      }

      // 更新菜单（登录状态）
      try {
        // 使用window.go.main.App直接调用，因为可能还没有生成TypeScript绑定
        await window.go.main.App.UpdateMenuAfterLogin();
        LogInfo("登录后更新菜单成功");
      } catch (menuError) {
        LogError(`更新菜单失败: ${menuError}`);
        // 继续登录流程，即使菜单更新失败
      }
    } catch (error) {
      LogError(`Login failed: ${error}`);
      throw error;
    }
  }, [setIsAuthenticated, setUsername]);

  const register = useCallback(async (email: string, password: string) => {
    try {
      await RegisterAPI(email, password);
      LogInfo(`注册邮箱成功: ${email}`);
    } catch (error) {
      LogError(`注册失败: ${error}`);
      throw error;
    }
  }, []);

  const logout = useCallback(() => {
    setIsAuthenticated(false);
    setUsername(null);
    setBalance(null);
    // We don't clear the saved credentials in the config file on logout
    // as they are meant to be persistent for future logins

    // 更新菜单（未登录状态）
    try {
      // 使用window.go.main.App直接调用，因为可能还没有生成TypeScript绑定
      window.go.main.App.UpdateMenuAfterLogout()
        .then(() => {
          LogInfo("登出后更新菜单成功");
        })
        .catch((menuError) => {
          LogError(`更新菜单失败: ${menuError}`);
        });
    } catch (menuError) {
      LogError(`更新菜单失败: ${menuError}`);
      // 继续登出流程，即使菜单更新失败
    }
  }, [setIsAuthenticated, setUsername, setBalance]);

  // Function to get saved credentials from the config file
  // Using useCallback to memoize the function and prevent unnecessary re-renders
  const getSavedCredentials = useCallback(async () => {
    try {
      const savedAuth = await GetAppAuth(); // Get app authentication from config file

      if (savedAuth && savedAuth.username) {
        // If we have a username, return it even if password is empty
        // This allows for pre-filling the username field without the password
        LogInfo('Found saved credentials in config file');
        return {
          username: savedAuth.username,
          password: savedAuth.password || '' // Handle empty password case
        };
      }
      return null;
    } catch (error) {
      LogError(`Error getting saved credentials: ${error}`);
      return null;
    }
  }, []);

  // 发送密码重置邮件
  const recoverPassword = useCallback(async (email: string) => {
    try {
      await RecoverPasswordAPI(email);
      LogInfo(`密码重置邮件发送成功: ${email}`);
    } catch (error) {
      LogError(`发送密码重置邮件失败: ${error}`);
      throw error;
    }
  }, []);

  // 验证密码重置令牌
  const verifyToken = useCallback(async (email: string, token: string) => {
    try {
      const response = await VerifyTokenAPI(email, token);
      LogInfo(`验证密码重置令牌成功: ${response.message}`);
      return response;
    } catch (error) {
      LogError(`验证密码重置令牌失败: ${error}`);
      throw error;
    }
  }, []);

  // 验证注册令牌
  const verifySignupToken = useCallback(async (email: string, token: string) => {
    try {
      const response = await VerifySignupTokenAPI(email, token);
      LogInfo(`验证注册令牌成功: ${response.message}`);
      return response;
    } catch (error) {
      LogError(`验证注册令牌失败: ${error}`);
      throw error;
    }
  }, []);

  // 更新用户密码
  const updatePassword = useCallback(async (password: string) => {
    try {
      await UpdatePasswordAPI(password);
      LogInfo('密码更新成功');
    } catch (error) {
      LogError(`更新密码失败: ${error}`);
      throw error;
    }
  }, []);

  return (
    <AuthContext.Provider value={{
      isAuthenticated,
      username,
      balance,
      isBalanceSufficient,
      login,
      register,
      logout,
      getSavedCredentials,
      refreshBalance,
      updateBalance,
      recoverPassword,
      verifyToken,
      verifySignupToken,
      updatePassword
    }}>
      {children}
    </AuthContext.Provider>
  );
};
