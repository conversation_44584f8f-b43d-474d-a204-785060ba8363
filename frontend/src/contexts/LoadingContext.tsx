import React, { createContext, useState, useContext, ReactNode } from 'react';
import { EventsOn, EventsOff } from '../../wailsjs/runtime/runtime';

interface LoadingContextType {
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

interface LoadingProviderProps {
  children: ReactNode;
}

export const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  React.useEffect(() => {
    // Listen for loading events from the Go backend
    EventsOn('loading:start', () => {
      setIsLoading(true);
    });

    EventsOn('loading:stop', () => {
      setIsLoading(false);
    });

    // Clean up event listeners on unmount
    return () => {
      EventsOff('loading:start', 'loading:stop');
    };
  }, []);

  const setLoading = (loading: boolean) => {
    setIsLoading(loading);
  };

  return (
    <LoadingContext.Provider value={{ isLoading, setLoading }}>
      {children}
    </LoadingContext.Provider>
  );
};