import "./App.css";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import LoadingSpinner from "./components/LoadingSpinner";
import { useAuth } from "./contexts/AuthContext";
import Login from "./pages/Login";
import Home from "./pages/Home";
import ProtectedRoute from "./components/ProtectedRoute";
import AboutDialog from "./components/AboutDialog";
import ClearDataDialog from "./components/ClearDataDialog";
import RechargeDialog from './components/RechargeDialog';
import PlaywrightInstallDialog from './components/PlaywrightInstallDialog';
import { useState, useEffect } from "react";
import { LogInfo, LogError } from "../wailsjs/runtime/runtime";

function App() {
  const { isAuthenticated } = useAuth();
  const [aboutDialogVisible, setAboutDialogVisible] = useState(false);
  const [clearDataDialogVisible, setClearDataDialogVisible] = useState(false);
  const [isRechargeDialogOpen, setIsRechargeDialogOpen] = useState(false);
  const [isPlaywrightInstallDialogOpen, setIsPlaywrightInstallDialogOpen] = useState(false);

  // 监听关于对话框事件
  useEffect(() => {
    const handleShowAbout = () => {
      LogInfo("显示关于对话框");
      setAboutDialogVisible(true);
    };

    // 注册事件监听器
    window.runtime.EventsOn("show:about", handleShowAbout);

    return () => {
      window.runtime.EventsOff("show:about", handleShowAbout);
    };
  }, []);

  // 监听清除数据对话框事件
  useEffect(() => {
    const handleShowClearData = () => {
      LogInfo("显示清除数据对话框");
      setClearDataDialogVisible(true);
    };

    // 注册事件监听器
    window.runtime.EventsOn("show:clearData", handleShowClearData);

    return () => {
      window.runtime.EventsOff("show:clearData", handleShowClearData);
    };
  }, []);

  // 修改为使用useEffect和事件监听
  useEffect(() => {
    const handleShowRecharge = () => {
      LogInfo("显示充值对话框");
      setIsRechargeDialogOpen(true);
    };

    // 注册事件监听器
    window.runtime.EventsOn("show:recharge", handleShowRecharge);

    return () => {
      window.runtime.EventsOff("show:recharge", handleShowRecharge);
    };
  }, []);

  // 监听Playwright安装对话框事件
  useEffect(() => {
    const handleShowPlaywrightInstall = () => {
      LogInfo("显示组件安装对话框");
      setIsPlaywrightInstallDialogOpen(true);
    };

    // 注册事件监听器
    window.runtime.EventsOn("show:playwrightInstall", handleShowPlaywrightInstall);

    return () => {
      window.runtime.EventsOff("show:playwrightInstall", handleShowPlaywrightInstall);
    };
  }, []);

  return (
    <BrowserRouter>
      <div id="App">
        {/* Global loading spinner */}
        <LoadingSpinner />

        <Routes>
          <Route
            path="/"
            element={
              isAuthenticated ? (
                <ProtectedRoute>
                  <Home />
                </ProtectedRoute>
              ) : (
                <Navigate to="/login" replace />
              )
            }
          />
          <Route
            path="/login"
            element={isAuthenticated ? <Navigate to="/" replace /> : <Login />}
          />
        </Routes>
        {/* 全局对话框 */}
        <AboutDialog visible={aboutDialogVisible} onClose={() => setAboutDialogVisible(false)} />
        <ClearDataDialog visible={clearDataDialogVisible} onClose={() => setClearDataDialogVisible(false)} />
        <RechargeDialog
          isOpen={isRechargeDialogOpen}
          onClose={() => setIsRechargeDialogOpen(false)}
        />
        <PlaywrightInstallDialog
          visible={isPlaywrightInstallDialogOpen}
          onClose={() => setIsPlaywrightInstallDialogOpen(false)}
        />
      </div>
    </BrowserRouter>
  );
}

export default App;
