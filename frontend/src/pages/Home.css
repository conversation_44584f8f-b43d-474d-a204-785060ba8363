.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('../assets/images/login-bg.svg') no-repeat center center;
  background-size: cover;
  position: relative;
  overflow: hidden;
}

.layout::before {
  content: "";
  position: absolute;
  width: 300%;
  height: 300%;
  top: -100%;
  left: -100%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%
  );
  background-size: 100px 100px;
  transform: rotate(45deg);
  z-index: 0;
  animation: login-bg-animation 60s linear infinite;
}

@keyframes login-bg-animation {
  0% {
    transform: rotate(45deg) translate(0, 0);
  }
  100% {
    transform: rotate(45deg) translate(-100px, -100px);
  }
}

.sub-layout {
  flex: 1;
  overflow: auto;
  position: relative;
  z-index: 1;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(54, 54, 54, 0.95);
  padding: 0 24px;
  height: 60px;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.logo {
  color: rgb(215, 247, 248);
  font-size: 15px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 8px;
  letter-spacing: 0.5px;
}

.user-info {
  color: white;
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
}

.user-info span {
  display: flex;
  align-items: center;
  height: 20px;
  padding: 2px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.user-info span:hover {
  background: rgba(255, 255, 255, 0.15);
}

.user-info .ant-btn-link {
  color: rgba(255, 255, 255, 0.85);
  padding: 4px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.user-info .ant-btn-link:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.content {
  padding: 10px;
  overflow-y: auto;
}

.content-container {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  max-width: 900px;
  margin: 0 auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  position: relative;
}

.content-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #1890ff, #52c41a);
}

.url-input-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.ant-select-selection-search {
  display: flex;
  align-items: center;
}

.screenshot-container {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.screenshot-container .ant-image {
  border: 1px solid rgba(240, 240, 240, 0.8);
  border-radius: 4px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.9);
}

.student-answer-container {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sider {
  background: transparent;
  padding: 10px;
}

.sider-content {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  position: relative;
}

.sider-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #1890ff, #52c41a);
}

.sider-textarea {
  margin-top: 10px;
}

.sider-textarea .ant-input {
  resize: none;
  height: 175px !important;
  border-radius: 8px;
}

.sider-textarea .ant-btn {
  margin-top: 10px;
  width: 100%;
  height: 45px;
  font-size: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.sider-textarea .ant-btn-primary {
  background: linear-gradient(90deg, #1890ff, #52c41a);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.sider-textarea .ant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(24, 144, 255, 0.4);
  background: linear-gradient(90deg, #40a9ff, #73d13d);
}

.footer {
  text-align: center;
  background: rgba(54, 54, 54, 0.8);
  color: white;
  padding: 15px 0;
  font-size: 11px;
  flex-shrink: 0;
  backdrop-filter: blur(5px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  width: 100%;
  padding: 0 20px;
}

.footer-progress {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.paper-progress {
  margin-left: 10px;
}

.footer-copyright {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.footer-left {
  flex: 1;
  display: flex;
  justify-content: flex-start;
}

.footer-right {
  flex: 1;
}

/* 区域选择器样式 */
.area-selector-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-container {
  position: relative;
  margin-bottom: 10px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 8px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.selection-area {
  position: absolute;
  pointer-events: none;
  z-index: 10;
}

/* 按钮通用样式 */
.ant-btn {
  transition: all 0.3s ease !important;
}

.ant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.ant-btn-primary {
  background: linear-gradient(90deg, #1890ff, #52c41a);
  border: none;
}

.ant-btn-primary:hover {
  background: linear-gradient(90deg, #40a9ff, #73d13d);
}

/* 极客风格自动阅卷按钮 */
.geek-button {
  position: relative;
  background: linear-gradient(45deg, #0c1db8, #020024, #090979, #00d4ff) !important;
  border: none !important;
  font-weight: bold !important;
  font-size: 16px !important;
  letter-spacing: 1px;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.5), inset 0 0 10px rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s ease !important;
  animation: pulse 2s infinite;
  transform-origin: center;
  min-width: 180px;
  height: 48px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 8px !important;
  color: white !important;
}

.geek-button::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  z-index: -1;
  background: linear-gradient(45deg, #ff0000, #ff7300, #fffb00, #48ff00, #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000);
  background-size: 400%;
  border-radius: 6px;
  animation: glowing 20s linear infinite;
}

.geek-button:hover {
  transform: scale(1.05) translateY(-2px);
  box-shadow: 0 0 25px rgba(0, 212, 255, 0.8), inset 0 0 15px rgba(255, 255, 255, 0.3) !important;
}

.geek-button:active {
  transform: scale(0.98);
}

.geek-button .anticon {
  font-size: 18px !important;
  margin-right: 8px !important;
  animation: iconPulse 1.5s infinite;
}

/* 停止阅卷状态 */
.geek-button.ant-btn-dangerous {
  background: linear-gradient(45deg, #b80c0c, #240202, #790909, #ff0000) !important;
  box-shadow: 0 0 15px rgba(255, 0, 0, 0.5), inset 0 0 10px rgba(255, 255, 255, 0.2) !important;
}

.geek-button.ant-btn-dangerous:hover {
  box-shadow: 0 0 25px rgba(255, 0, 0, 0.8), inset 0 0 15px rgba(255, 255, 255, 0.3) !important;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.5), inset 0 0 10px rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.8), inset 0 0 15px rgba(255, 255, 255, 0.3);
  }
  100% {
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.5), inset 0 0 10px rgba(255, 255, 255, 0.2);
  }
}

@keyframes iconPulse {
  0% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.8;
    transform: scale(1);
  }
}

@keyframes glowing {
  0% {
    background-position: 0 0;
  }
  50% {
    background-position: 400% 0;
  }
  100% {
    background-position: 0 0;
  }
}
