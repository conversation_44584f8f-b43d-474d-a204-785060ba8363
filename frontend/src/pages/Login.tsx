import React, { useState, useEffect, useCallback } from 'react';
import { Form, Input, Button, Card, Typography, message, Checkbox, Spin, Image, Tabs, Modal } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import { useLoading } from '../contexts/LoadingContext';
import { LogInfo, LogError } from '../../wailsjs/runtime/runtime';
import { CheckVersion } from '../../wailsjs/go/main/APIService';
import { GetVersion } from '../../wailsjs/go/main/App';
import UpdateDialog from '../components/UpdateDialog';
import PasswordResetDialog from '../components/PasswordResetDialog';
import SignupVerificationDialog from '../components/SignupVerificationDialog';
import './Login.css';
import LogoImage from '../assets/images/appicon.png';
import QRCodeImage from '../assets/images/qrcode_258.jpg';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

interface LoginFormValues {
  username: string;
  password: string;
  remember: boolean;
}

interface RegisterFormValues {
  email: string;
  password: string;
  confirmPassword: string;
}

const Login: React.FC = () => {
  const { login, register, getSavedCredentials } = useAuth();
  const { isLoading, setLoading } = useLoading();
  const [loginForm] = Form.useForm();
  const [registerForm] = Form.useForm();
  const [formInitialized, setFormInitialized] = useState(false);
  const [currentVersion, setCurrentVersion] = useState<string>("");
  const [updateDialogVisible, setUpdateDialogVisible] = useState<boolean>(false);
  const [registerSuccess, setRegisterSuccess] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>("login");
  const [passwordResetVisible, setPasswordResetVisible] = useState<boolean>(false);
  const [signupVerificationVisible, setSignupVerificationVisible] = useState<boolean>(false);
  const [registeredEmail, setRegisteredEmail] = useState<string>("");
  const [versionInfo, setVersionInfo] = useState<{
    version: string;
    downloadUrl: string;
    forceUpdate: boolean;
    updateLog: string;
  }>({
    version: "",
    downloadUrl: "",
    forceUpdate: false,
    updateLog: ""
  });

  // Load saved credentials when component mounts
  // This effect should only run once when the component mounts
  useEffect(() => {
    const loadSavedCredentials = async () => {
      try {
        setLoading(true);
        const savedCredentials = await getSavedCredentials();

        if (savedCredentials) {
          // Pre-fill the form with saved credentials
          loginForm.setFieldsValue({
            username: savedCredentials.username,
            password: savedCredentials.password,
            remember: true
          });
          LogInfo('Pre-filled login form with saved credentials');
        }
        setFormInitialized(true);
      } catch (error) {
        LogError(`Error loading saved credentials: ${error}`);
        setFormInitialized(true); // Still set form as initialized even if there's an error
      } finally {
        setLoading(false);
      }
    };

    loadSavedCredentials();
    // We're using the memoized version of getSavedCredentials now, so it won't change
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 检查版本更新
  useEffect(() => {
    const checkForUpdates = async () => {
      try {
        // 获取当前版本
        const currentVer = await GetVersion();
        setCurrentVersion(currentVer);

        // 检查最新版本
        const latestVersionInfo = await CheckVersion();

        // 转换属性名称以匹配前端组件需要的格式
        const formattedVersionInfo = {
          version: latestVersionInfo.version,
          downloadUrl: latestVersionInfo.download_url,
          forceUpdate: latestVersionInfo.force_update,
          updateLog: latestVersionInfo.update_log
        };

        setVersionInfo(formattedVersionInfo);

        // 比较版本号，如果有新版本则显示更新对话框
        if (currentVer !== formattedVersionInfo.version) {
          LogInfo(`发现新版本: ${formattedVersionInfo.version}, 当前版本: ${currentVer}`);
          setUpdateDialogVisible(true);
        }
      } catch (error) {
        LogError(`检查更新失败: ${error}`);
        // 版本检查失败不影响登录流程，只是记录错误
      }
    };

    checkForUpdates();
  }, []);

  const onLoginFinish = useCallback(async (values: LoginFormValues) => {
    setLoading(true);
    try {
      await login(values.username, values.password, values.remember);
      message.success('登录成功！');
    } catch (err) {
      LogError(`登录失败: ${err}`);
      const errorMessage = err instanceof Error ? err.message : String(err);

      // 提供更友好的错误信息
      let userFriendlyMessage = "登录失败";

      // 处理特定错误码
      if (errorMessage.includes("401")) {
        userFriendlyMessage = "用户名或密码错误，请重新输入";
      } else if (errorMessage.includes("403")) {
        userFriendlyMessage = "您没有访问权限，请联系管理员";
      } else if (errorMessage.includes("login failed with status")) {
        userFriendlyMessage = "登录服务暂时不可用，请稍后再试";
      } else if (errorMessage.includes("请先登录")) {
        userFriendlyMessage = "登录会话已过期，请重新登录";
      } else if (errorMessage.includes("network") || errorMessage.includes("timeout")) {
        userFriendlyMessage = "网络连接异常，请检查网络后重试";
      }

      message.error(userFriendlyMessage);
    } finally {
      setLoading(false);
    }
  }, [login, setLoading]);

  // 显示注册成功对话框
  const showRegisterSuccessModal = useCallback(() => {
    Modal.success({
      title: '注册验证成功',
      content: (
        <div>
          <p>您的账号已成功激活，并获得500免费积分！</p>
          <p>关注山竹阅卷公众号，领取更多免费体验积分。</p>
          <Image
            src={QRCodeImage}
            alt="山竹阅卷公众号二维码"
            style={{ width: '200px', height: '200px', margin: '16px auto' }}
          />
        </div>
      ),
      onOk: () => {
        // 切换到登录标签页，并预填充邮箱
        setActiveTab('login');
        loginForm.setFieldsValue({ username: registeredEmail, password: '', remember: true });
      }
    });
  }, [loginForm, registeredEmail, setActiveTab]);

  const onRegisterFinish = useCallback(async (values: RegisterFormValues) => {
    setLoading(true);
    try {
      await register(values.email, values.password);
      LogInfo(`注册成功: ${values.email}`);
      setRegisterSuccess(true);
      setRegisteredEmail(values.email);

      // 直接显示验证对话框
      setSignupVerificationVisible(true);
      registerForm.resetFields();
    } catch (err) {
      LogError(`注册失败: ${err}`);
      const errorMessage = err instanceof Error ? err.message : String(err);
      // 提供更友好的错误信息
      let userFriendlyMessage = "注册失败";
      // 处理特定错误码
      if (errorMessage.includes("409")) {
        userFriendlyMessage = "该邮箱已被注册，请直接登录或使用其他邮箱";
      } else if (errorMessage.includes("400")) {
        userFriendlyMessage = "请检查您的输入是否符合要求";
      } else if (errorMessage.includes("register failed with status")) {
        userFriendlyMessage = "注册服务暂时不可用，请稍后再试";
      } else if (errorMessage.includes("network") || errorMessage.includes("timeout")) {
        userFriendlyMessage = "网络连接异常，请检查网络后重试";
      }

      message.error(userFriendlyMessage);
    } finally {
      setLoading(false);
    }
  }, [register, setLoading, loginForm, registerForm]);

  // 验证密码是否包含字母和数字
  const validatePassword = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error('请输入密码'));
    }
    if (value.length < 8) {
      return Promise.reject(new Error('密码长度至少为8位'));
    }
    if (!/[a-zA-Z]/.test(value) || !/[0-9]/.test(value)) {
      return Promise.reject(new Error('密码必须同时包含字母和数字'));
    }
    return Promise.resolve();
  };

  return (
    <div className="login-container">
      <Card className="login-card">
        <div className="login-header">
          <Image
            src={LogoImage}
            alt="山竹阅卷 Logo"
            preview={false}
            width={80}
            style={{ marginBottom: 15 }}
          />
          <Title level={2}>山竹阅卷</Title>
          <Title level={4}>智能评分系统</Title>
        </div>

        {formInitialized ? (
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            centered
            className="login-tabs"
          >
            <TabPane tab="登录" key="login">
              <Form
                form={loginForm}
                name="login"
                initialValues={{ remember: true }}
                onFinish={onLoginFinish}
                layout="vertical"
                size="large"
                className="login-form"
              >
                <Form.Item
                  name="username"
                  rules={[
                    { required: true, message: '请输入用户名!' },
                    { pattern: /^\S+$/, message: '用户名不能包含空格!' }
                  ]}
                >
                  <Input
                    className="login-input"
                    prefix={<UserOutlined style={{ color: '#1890ff' }} />}
                    placeholder="请输入用户名"
                    autoComplete="username"
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[
                    { required: true, message: '请输入密码!' },
                    { pattern: /^\S+$/, message: '密码不能包含空格!' }
                  ]}
                >
                  <Input.Password
                    className="login-input"
                    prefix={<LockOutlined style={{ color: '#1890ff' }} />}
                    placeholder="请输入密码"
                    autoComplete="current-password"
                  />
                </Form.Item>

                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Form.Item name="remember" valuePropName="checked" initialValue={true} className="login-checkbox" style={{ marginBottom: 0 }}>
                    <Checkbox>记住登录</Checkbox>
                  </Form.Item>
                  <Button
                    type="link"
                    onClick={() => setPasswordResetVisible(true)}
                    style={{ padding: 0 }}
                  >
                    忘记密码?
                  </Button>
                </div>

                <Form.Item>
                  <Button type="primary" htmlType="submit" className="login-button" loading={isLoading}>
                    登录
                  </Button>
                </Form.Item>
              </Form>
            </TabPane>

            <TabPane tab="注册" key="register">
              <Form
                form={registerForm}
                name="register"
                onFinish={onRegisterFinish}
                layout="vertical"
                size="large"
                className="login-form"
              >
                <Form.Item
                  name="email"
                  rules={[
                    { required: true, message: '请输入邮箱!' },
                    { type: 'email', message: '请输入有效的邮箱格式!' }
                  ]}
                >
                  <Input
                    className="login-input"
                    prefix={<MailOutlined style={{ color: '#1890ff' }} />}
                    placeholder="请输入邮箱"
                    autoComplete="email"
                  />
                </Form.Item>

                <Form.Item
                  name="password"
                  rules={[
                    { validator: validatePassword }
                  ]}
                >
                  <Input.Password
                    className="login-input"
                    prefix={<LockOutlined style={{ color: '#1890ff' }} />}
                    placeholder="请输入密码"
                    autoComplete="new-password"
                  />
                </Form.Item>

                <Form.Item
                  name="confirmPassword"
                  dependencies={['password']}
                  rules={[
                    { required: true, message: '请确认密码!' },
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        if (!value || getFieldValue('password') === value) {
                          return Promise.resolve();
                        }
                        return Promise.reject(new Error('两次输入的密码不一致!'));
                      },
                    }),
                  ]}
                >
                  <Input.Password
                    className="login-input"
                    prefix={<LockOutlined style={{ color: '#1890ff' }} />}
                    placeholder="请确认密码"
                    autoComplete="new-password"
                  />
                </Form.Item>

                <Form.Item style={{ marginTop: 20 }}>
                  <Button type="primary" htmlType="submit" className="login-button" loading={isLoading}>
                    注册
                  </Button>
                </Form.Item>
              </Form>
            </TabPane>
          </Tabs>
        ) : (
          <div className="loading-placeholder">
            <Spin size="large" />
            <div style={{ marginTop: 15 }}>正在加载...</div>
          </div>
        )}
      </Card>

      {/* 版本更新对话框 */}
      <UpdateDialog
        visible={updateDialogVisible}
        onClose={() => setUpdateDialogVisible(false)}
        versionInfo={versionInfo}
        currentVersion={currentVersion}
      />

      {/* 密码重置对话框 */}
      <PasswordResetDialog
        isOpen={passwordResetVisible}
        onClose={() => setPasswordResetVisible(false)}
      />

      {/* 注册验证对话框 */}
      <SignupVerificationDialog
        isOpen={signupVerificationVisible}
        onClose={() => setSignupVerificationVisible(false)}
        email={registeredEmail}
        onVerificationSuccess={showRegisterSuccessModal}
      />
    </div>
  );
};

export default Login;
