import React, { useState } from "react";
import { Modal, Form, Input, Button, message, Steps, Result } from "antd";
import { MailOutlined, LockOutlined, KeyOutlined } from "@ant-design/icons";
import { useAuth } from "../contexts/AuthContext";
import { useLoading } from "../contexts/LoadingContext";
import { LogError, LogInfo } from "../../wailsjs/runtime/runtime";

interface PasswordResetDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const { Step } = Steps;

const PasswordResetDialog: React.FC<PasswordResetDialogProps> = ({ isOpen, onClose }) => {
  const { recoverPassword, verifyToken, updatePassword } = useAuth();
  const { isLoading, setLoading } = useLoading();
  const [emailForm] = Form.useForm();
  const [tokenForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [email, setEmail] = useState<string>("");
  const [resetToken, setResetToken] = useState<string>("");
  const [tokenVerified, setTokenVerified] = useState<boolean>(false);
  const [resetSuccess, setResetSuccess] = useState<boolean>(false);
  const [verifyingToken, setVerifyingToken] = useState<boolean>(false);

  // 处理关闭对话框
  const handleClose = () => {
    // 重置表单和状态
    emailForm.resetFields();
    tokenForm.resetFields();
    passwordForm.resetFields();
    setCurrentStep(0);
    setEmail("");
    setResetToken("");
    setTokenVerified(false);
    setResetSuccess(false);
    setVerifyingToken(false);
    onClose();
  };

  // 发送密码重置邮件
  const handleSendResetEmail = async (values: { email: string }) => {
    try {
      setLoading(true);
      await recoverPassword(values.email);
      setEmail(values.email);
      setCurrentStep(1);
      message.success("密码重置邮件已发送，请查收邮件");
      LogInfo(`密码重置邮件已发送至: ${values.email}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      LogError(`发送密码重置邮件失败: ${errorMessage}`);

      // 提供更友好的错误信息
      let userFriendlyMessage = "发送密码重置邮件失败";

      if (errorMessage.includes("404")) {
        userFriendlyMessage = "该邮箱未注册，请先注册";
      } else if (errorMessage.includes("429")) {
        userFriendlyMessage = "请求过于频繁，请稍后再试";
      } else if (errorMessage.includes("network") || errorMessage.includes("timeout")) {
        userFriendlyMessage = "网络连接异常，请检查网络后重试";
      }

      message.error(userFriendlyMessage);
    } finally {
      setLoading(false);
    }
  };

  // 验证令牌
  const handleVerifyToken = async (values: { token: string }) => {
    try {
      setVerifyingToken(true);
      const response = await verifyToken(email, values.token);

      if (response.success) {
        setResetToken(values.token);
        setTokenVerified(true);
        setCurrentStep(2);
        message.success("令牌验证成功，请设置新密码");
        LogInfo(`令牌验证成功: ${response.message}`);
      } else {
        message.error("令牌验证失败，请检查后重试");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      LogError(`验证令牌失败: ${errorMessage}`);

      // 提供更友好的错误信息
      let userFriendlyMessage = "验证令牌失败";

      if (errorMessage.includes("400")) {
        userFriendlyMessage = "令牌无效，请检查后重试";
      } else if (errorMessage.includes("401")) {
        userFriendlyMessage = "令牌已过期，请重新发送重置邮件";
      } else if (errorMessage.includes("network") || errorMessage.includes("timeout")) {
        userFriendlyMessage = "网络连接异常，请检查网络后重试";
      }

      message.error(userFriendlyMessage);
    } finally {
      setVerifyingToken(false);
    }
  };

  // 验证密码是否包含字母和数字
  const validatePassword = (_: any, value: string) => {
    if (!value) {
      return Promise.reject(new Error("请输入密码"));
    }
    if (value.length < 8) {
      return Promise.reject(new Error("密码长度至少为8位"));
    }
    if (!/[a-zA-Z]/.test(value) || !/[0-9]/.test(value)) {
      return Promise.reject(new Error("密码必须同时包含字母和数字"));
    }
    return Promise.resolve();
  };

  // 更新密码
  const handleUpdatePassword = async (values: { password: string }) => {
    try {
      setLoading(true);
      await updatePassword(values.password);
      setResetSuccess(true);
      setCurrentStep(3);
      LogInfo("密码重置成功");
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      LogError(`密码重置失败: ${errorMessage}`);

      // 提供更友好的错误信息
      let userFriendlyMessage = "密码重置失败";

      if (errorMessage.includes("401")) {
        userFriendlyMessage = "令牌已过期，请重新验证";
        // 回到验证令牌步骤
        setCurrentStep(1);
        setTokenVerified(false);
      } else if (errorMessage.includes("400")) {
        userFriendlyMessage = "密码格式不符合要求，请确保密码至少8位且包含字母和数字";
      } else if (errorMessage.includes("network") || errorMessage.includes("timeout")) {
        userFriendlyMessage = "网络连接异常，请检查网络后重试";
      }

      message.error(userFriendlyMessage);
    } finally {
      setLoading(false);
    }
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Form
            form={emailForm}
            name="passwordResetEmail"
            onFinish={handleSendResetEmail}
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="email"
              rules={[
                { required: true, message: "请输入邮箱!" },
                { type: "email", message: "请输入有效的邮箱格式!" },
              ]}
            >
              <Input
                prefix={<MailOutlined style={{ color: "#1890ff" }} />}
                placeholder="请输入注册邮箱"
                autoComplete="email"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                style={{ width: "100%" }}
              >
                发送重置邮件
              </Button>
            </Form.Item>
          </Form>
        );
      case 1:
        return (
          <Form
            form={tokenForm}
            name="verifyToken"
            onFinish={handleVerifyToken}
            layout="vertical"
            size="large"
          >
            <p>我们已向 {email} 发送了一封包含重置令牌的邮件。</p>
            <p>请从邮件中获取重置令牌，并在下方输入。</p>

            <Form.Item
              name="token"
              rules={[{ required: true, message: "请输入重置令牌!" }]}
            >
              <Input
                prefix={<KeyOutlined style={{ color: "#1890ff" }} />}
                placeholder="请输入重置令牌"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={verifyingToken}
                style={{ width: "100%" }}
              >
                验证令牌
              </Button>
            </Form.Item>
          </Form>
        );
      case 2:
        return (
          <Form
            form={passwordForm}
            name="passwordReset"
            onFinish={handleUpdatePassword}
            layout="vertical"
            size="large"
          >
            <p>令牌验证成功，请设置新密码。</p>

            <Form.Item
              name="password"
              rules={[{ validator: validatePassword }]}
            >
              <Input.Password
                prefix={<LockOutlined style={{ color: "#1890ff" }} />}
                placeholder="请输入新密码"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              dependencies={["password"]}
              rules={[
                { required: true, message: "请确认密码!" },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue("password") === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error("两次输入的密码不一致!"));
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined style={{ color: "#1890ff" }} />}
                placeholder="请确认新密码"
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={isLoading}
                style={{ width: "100%" }}
              >
                重置密码
              </Button>
            </Form.Item>
          </Form>
        );
      case 3:
        return (
          <Result
            status="success"
            title="密码重置成功!"
            subTitle="您现在可以使用新密码登录了"
            extra={[
              <Button type="primary" key="login" onClick={handleClose}>
                返回登录
              </Button>,
            ]}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Modal
      title="密码重置"
      open={isOpen}
      onCancel={handleClose}
      footer={null}
      maskClosable={false}
      width={500}
    >
      <Steps current={currentStep} style={{ marginBottom: 20 }}>
        <Step title="发送邮件" />
        <Step title="验证令牌" />
        <Step title="重置密码" />
        <Step title="完成" />
      </Steps>
      {renderStepContent()}
    </Modal>
  );
};

export default PasswordResetDialog;
