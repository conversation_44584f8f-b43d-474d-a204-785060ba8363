import React from "react";
import { Modal, Typography, Space, Button } from "antd";
import { InstallPlaywright } from "../../wailsjs/go/main/App";
import { LogInfo, LogError } from "../../wailsjs/runtime/runtime";

const { Title, Paragraph, Text } = Typography;

interface PlaywrightInstallDialogProps {
  visible: boolean;
  onClose: () => void;
}

const PlaywrightInstallDialog: React.FC<PlaywrightInstallDialogProps> = ({
  visible,
  onClose,
}) => {
  const [installing, setInstalling] = React.useState(false);

  const handleInstall = async () => {
    try {
      setInstalling(true);
      LogInfo("开始安装组件");
      await InstallPlaywright();
      LogInfo("组件安装成功");
      setInstalling(false);
      onClose();
    } catch (error) {
      LogError(`安装组件失败: ${error}`);
      setInstalling(false);
    }
  };

  return (
    <Modal
      title="安装必要组件"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={500}
      centered
      maskClosable={false}
      closable={!installing}
    >
      <Space direction="vertical" style={{ width: "100%" }}>
        <Title level={4}>需要安装必要组件</Title>
        <Paragraph>
          山竹阅卷需要安装必要组件才能正常运行。
        </Paragraph>
        <Paragraph>
          <Text strong>注意：</Text> 安装过程可能需要几分钟时间，请耐心等待。安装完成后，应用将自动重新加载。
        </Paragraph>
        <div style={{ display: "flex", justifyContent: "flex-end", marginTop: 16 }}>
          <Button onClick={onClose} disabled={installing} style={{ marginRight: 8 }}>
            稍后安装
          </Button>
          <Button type="primary" onClick={handleInstall} loading={installing}>
            立即安装
          </Button>
        </div>
      </Space>
    </Modal>
  );
};

export default PlaywrightInstallDialog;
