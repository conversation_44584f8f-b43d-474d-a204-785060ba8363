package main

import (
	"encoding/json"
	"fmt"
)

type GuangdaResponse struct {
	Result struct {
		VKs []struct {
			MH        string `json:"mh"`
			ImageData struct {
				VUrl []string `json:"vUrl"`
			} `json:"imageData"`
		} `json:"vKs"`
	} `json:"result"`
}

func getImageUrlMap(jsonData []byte) (map[string]string, error) {
	var resp GuangdaResponse
	if err := json.Unmarshal(jsonData, &resp); err != nil {
		return nil, err
	}

	// 检查是否有数据
	if len(resp.Result.VKs) == 0 {
		return nil, fmt.Errorf("vKs列表为空")
	}

	// 创建map存储MH到URL的映射
	urlMap := make(map[string]string)

	// 遍历所有VKs项，提取MH和URL
	for _, vk := range resp.Result.VKs {
		// 检查是否有URL
		if len(vk.ImageData.VUrl) > 0 {
			// 将MH作为key，URL作为value添加到map中
			urlMap[vk.MH] = vk.ImageData.VUrl[0]
		}
	}

	// 检查map是否为空
	if len(urlMap) == 0 {
		return nil, fmt.Errorf("未找到有效的图片URL")
	}

	return urlMap, nil
}
