package main

import (
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
)

// downloadImage 下载图片并转换为base64编码
// imageURL: 图片URL
// maxWidth: 最大宽度，如果为0则保持原始宽度
// 返回: (base64编码的图片, 错误)
func downloadImage(imageURL string, maxWidth int) (string, error) {
	// 创建HTTP请求
	resp, err := http.Get(imageURL)
	if err != nil {
		return "", fmt.Erro<PERSON>("下载图片失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("下载图片失败，状态码: %d", resp.StatusCode)
	}

	// 检查Content-Type头信息，确定图片类型
	contentType := resp.Header.Get("Content-Type")

	// 读取响应体
	imageBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取图片数据失败: %v", err)
	}

	// 使用resizeImage函数调整图片大小
	var processedBytes []byte
	var format string
	compressedBytes, format, err := resizeImage(imageBytes, maxWidth)
	if err != nil {
		if AppLogger != nil {
			AppLogger.Warning("压缩图片失败: %v, 使用原图", err)
		}
		processedBytes = imageBytes
		// 尝试从响应头中推断格式
		if contentType == "image/jpeg" || contentType == "image/jpg" {
			format = "jpeg"
		} else {
			format = "png" // 默认为PNG
		}
	} else {
		processedBytes = compressedBytes
	}

	// 将图像转换为base64编码
	base64Str := base64.StdEncoding.EncodeToString(processedBytes)

	// 添加data URL前缀，使用正确的MIME类型
	var dataURL string
	if format == "jpeg" || format == "jpg" {
		dataURL = "data:image/jpeg;base64," + base64Str
	} else {
		dataURL = "data:image/png;base64," + base64Str
	}

	return dataURL, nil
}
