# 密码重置 API 文档

本文档描述了密码重置功能的 API 接口。

## 1. 发送密码重置邮件

当用户忘记密码时，可以使用此 API 发送密码重置邮件。

**请求**:

- 方法: `POST`
- 路径: `/api/v1/auth/recover`
- 需要认证: 否

**请求体**:

```json
{
  "email": "<EMAIL>"
}
```

**响应**:

```json
{
  "success": true,
  "message": "密码重置邮件已发送，请检查您的邮箱"
}
```

**错误响应**:

- 400 Bad Request: 请求格式错误或缺少必要参数
- 500 Internal Server Error: 服务器内部错误

## 2. 验证密码重置令牌

用户收到密码重置邮件后，需要使用此 API 验证令牌并获取访问令牌。

**请求**:

- 方法: `POST`
- 路径: `/api/v1/auth/verify`
- 需要认证: 否

**请求体**:

```json
{
  "email": "<EMAIL>",
  "token": "YOUR_RECOVERY_TOKEN",
  "type": "recovery"
}
```

**响应**:

```json
{
  "success": true,
  "message": "令牌验证成功",
  "access_token": "YOUR_ACCESS_TOKEN",
  "refresh_token": "YOUR_REFRESH_TOKEN",
  "expires_in": 3600,
  "expires_at": 1609459200
}
```

**响应头**:

```text
X-Access-Token: YOUR_ACCESS_TOKEN
X-Refresh-Token: YOUR_REFRESH_TOKEN
```

**错误响应**:

- 400 Bad Request: 请求格式错误或令牌无效
- 500 Internal Server Error: 服务器内部错误

## 3. 更新用户密码

验证令牌成功后，可以使用此 API 更新密码。

**请求**:

- 方法: `POST`
- 路径: `/api/v1/auth/update-password`
- 需要认证: 是（需要在请求头中包含有效的访问令牌）

**请求头**:

```text
X-Access-Token: Bearer YOUR_ACCESS_TOKEN
```

**请求体**:

```json
{
  "password": "new_password"
}
```

**响应**:

```json
{
  "success": true,
  "message": "密码已成功更新"
}
```

**错误响应**:

- 400 Bad Request: 请求格式错误或缺少必要参数
- 401 Unauthorized: 未提供有效的访问令牌
- 500 Internal Server Error: 服务器内部错误

## 密码重置流程

1. 用户在登录页面点击"忘记密码"
2. 用户输入邮箱地址，前端调用 `/api/v1/auth/recover` API
3. 用户收到密码重置邮件，复制邮件中的 token 到"忘记密码"页面
4. 用户输入 token 和新密码等信息，首先调用 `/api/v1/auth/verify` API 获取访问令牌
5. 前端调用 `/api/v1/auth/update-password` API，并在请求头中包含访问令牌
6. 密码更新成功后，用户可以使用新密码登录
7. 假如密码更新失败，弹出提示框

## 注意事项

- 密码重置邮件的有效期通常为 24 小时，过期后需要重新请求
- 密码应符合安全要求，至少包含 8 个字符，并包含字母和数字
- 验证令牌成功后，前端应立即保存返回的访问令牌，用于后续的密码更新请求
