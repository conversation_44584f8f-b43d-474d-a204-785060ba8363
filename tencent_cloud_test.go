package main

import (
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"testing"
	"time"
)

func TestTencentCloudClient(t *testing.T) {
	// 初始化测试参数
	uin := "100016998379"
	secretId := "AKIDpPp592kGZXLvJTSEn9IG4pV0IPwKfppf"
	secretKey := "qYAb2MZdXIrGUXaWP6cv0sXAew1vc6MO"
	host := "1304480945-494z1azhw3.ap-guangzhou.tencentscf.com"
	algorithm := "TC3-HMAC-SHA256"
	service := "scf"
	timestamp := time.Now().Unix()
	payload := "{\"email\": \"<EMAIL>\",\"password\": \"WoIAGHjEgYWRTUQIPWNN\"}"
	canonicalURI := "/api/v1/auth/login"
	httpRequestMethod := "POST"

	// 步骤1: 构建规范请求字符串
	canonicalRequest := buildCanonicalRequest(httpRequestMethod, canonicalURI, "", host, payload)
	fmt.Println("canonicalRequest => ", canonicalRequest)

	// 步骤2: 构建待签名字符串
	stringToSign := buildStringToSign(algorithm, timestamp, service, canonicalRequest)
	fmt.Println("stringToSign => ", stringToSign)

	// 步骤3: 计算签名
	signature := calculateSignature(secretKey, stringToSign, timestamp, service)
	fmt.Println("signature => ", signature)

	// 步骤4: 构建授权头
	authorization := buildAuthorizationHeader(
		algorithm, secretId, timestamp, service, "content-type;host", signature)
	fmt.Println("authorization => ", authorization)

	// curl := fmt.Sprintf(`curl -X %s https://%s%s -H "Authorization: %s" -H "Content-Type: application/json" -H "Host: %s"  -H "X-Scf-Cam-Uin: %s" -H "X-Scf-Cam-Timestamp: %d" -d '%s'`,
	// 	httpRequestMethod, host, canonicalURI, authorization, host, uin, timestamp, payload)
	// fmt.Println(curl)
	// // 步骤5: 执行curl
	// if err := executeCurl(curl);err != nil {
	// 	t.Errorf("curl执行失败: %v", err)
	// }

	// 步骤6: 执行API请求
	if err := executeAPIRequest(httpRequestMethod, canonicalURI, host, authorization, uin, timestamp, payload); err != nil {
		t.Errorf("API请求执行失败: %v", err)
	}
}

// func executeCurl(curlCommand string) error {
// 	fmt.Println("执行curl命令:", curlCommand)
// 	cmd := exec.Command("bash", "-c", curlCommand)
// 	output, err := cmd.CombinedOutput()
// 	if err != nil {
// 		return fmt.Errorf("curl执行失败: %v, 输出: %s", err, string(output))
// 	}
// 	fmt.Println("curl执行成功:", string(output))
// 	return nil
// }

func executeAPIRequest(method, canonicalURI, host, authorization, uin string, timestamp int64, payload string) error {
	client := &http.Client{}
	req, err := http.NewRequest(method, fmt.Sprintf("https://%s%s", host, canonicalURI), strings.NewReader(payload))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置必要的请求头
	req.Header.Set("Authorization", authorization)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Host", host)
	req.Header.Set("X-Scf-Cam-Uin", uin)
	req.Header.Set("X-Scf-Cam-Timestamp", strconv.FormatInt(timestamp, 10))

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("请求发送失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode >= 400 {
		return fmt.Errorf("API请求失败: 状态码 %d, 响应: %s", resp.StatusCode, string(body))
	}
	fmt.Printf("API请求成功: %s\n", string(body))
	return nil
}
