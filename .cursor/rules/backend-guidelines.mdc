---
description: 
globs: 
alwaysApply: true
---
# Go后端开发指南

## 项目结构
1. 主要文件：
   - [main.go](mdc:main.go): 应用入口
   - [app.go](mdc:app.go): 核心业务逻辑
   - [menu.go](mdc:menu.go): 菜单配置

## 代码组织
1. 使用包级别的组织方式
2. 相关功能放在同一个包中
3. 使用接口定义服务契约

## 命名规范
1. 包名使用小写字母
2. 函数名使用驼峰命名
3. 结构体名称使用驼峰命名
4. 接口名称以er结尾（如：`Reader`、`Writer`）

## 错误处理
1. 使用Go标准错误处理方式
```go
if err != nil {
    return fmt.Errorf("操作失败: %v", err)
}
```

2. 自定义错误类型
```go
type AppError struct {
    Code    int
    Message string
}
```

## 配置管理
1. 使用配置文件存储应用配置
2. 使用环境变量进行环境特定配置
3. 敏感信息使用加密存储

## 数据库操作
1. 使用GORM进行数据库操作
2. 定义清晰的数据模型
3. 使用事务确保数据一致性

## 日志记录
1. 使用结构化日志
2. 记录关键操作和错误
3. 使用不同的日志级别

## Wails集成
1. 前端调用后端方法
```go
func (a *App) SomeMethod() error {
    // 实现逻辑
}
```

2. 后端发送事件到前端
```go
runtime.EventsEmit(a.ctx, "event:name", data)
```

3. 菜单配置
```go
func CreateAppMenu(app *App) *menu.Menu {
    // 菜单配置
}
```

## 安全性
1. 输入验证
2. 数据加密
3. 权限控制
4. 防止SQL注入
