---
description: 
globs: 
alwaysApply: true
---
# 项目结构指南

## 技术栈
- 后端：Go + Wails
- 前端：React + Ant Design
- 数据库：SQLite

## 主要目录结构
- `frontend/`: 前端代码目录
  - `src/`: React源代码
    - `components/`: React组件
    - `pages/`: 页面组件
    - `contexts/`: React上下文
    - `App.tsx`: 应用入口组件
- `main.go`: 后端主入口文件
- `app.go`: 应用核心逻辑
- `menu.go`: 应用菜单配置

## 关键文件说明
- [main.go](mdc:main.go): Wails应用的主入口，负责初始化应用和配置
- [app.go](mdc:app.go): 包含应用的核心业务逻辑
- [menu.go](mdc:menu.go): 定义应用的菜单结构
- [frontend/src/App.tsx](mdc:frontend/src/App.tsx): React应用的主组件
- [frontend/src/components/](mdc:frontend/src/components): 包含所有可复用的React组件

## 开发规范
1. 前端组件使用TypeScript编写
2. 使用Ant Design组件库进行UI开发
3. 后端使用Go标准库和Wails框架
4. 使用SQLite进行数据存储
5. 遵循Go的标准代码规范
