package main

import (
	"testing"
	"time"
)

func TestGradingRecordsToMarkdown(t *testing.T) {
	// 创建测试数据
	records := []GradingRecordListItem{
		{
			CreatedAt:       time.Now(),
			UserEmail:       "<EMAIL>",
			AnswerImageHash: "hash1",
			Score:           85.5,
			AnswerImage:     "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==",
			GradingCriteria: "1. 理解题意 (30分)\n2. 分析问题 (40分)\n3. 解决方案 (30分)",
			AnswerText:      "这是学生的答案内容...",
			ScoreDetails:    "理解题意: 25分\n分析问题: 35分\n解决方案: 25.5分",
		},
		{
			CreatedAt:       time.Now().Add(-24 * time.Hour),
			UserEmail:       "<EMAIL>",
			AnswerImageHash: "hash2",
			Score:           92.0,
			AnswerImage:     "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==",
			GradingCriteria: "评分标准：\n1. 概念理解 (50分)\n2. 应用能力 (50分)",
			AnswerText:      "另一个学生的答案...",
			ScoreDetails:    "概念理解: 45分\n应用能力: 47分",
		},
	}

	// 调用函数
	markdown := GradingRecordsToMarkdown(records)

	// 简单检查结果是否包含预期的内容
	expectedContents := []string{
		"# 阅卷记录汇总",
		"共 2 条记录",
		"| 创建时间 | 用户 | 分数 | 评分标准 |",
		"<EMAIL>",
		"<EMAIL>",
		"85.5",
		"92.0",
		"### 记录 1",
		"### 记录 2",
		"- **评分标准**:",
		"- **学生答案**:",
		"- **评分细节**:",
	}

	for _, expected := range expectedContents {
		if !contains(markdown, expected) {
			t.Errorf("生成的Markdown应该包含 '%s'，但没有找到", expected)
		}
	}

	// 检查空记录情况
	emptyMarkdown := GradingRecordsToMarkdown([]GradingRecordListItem{})
	if emptyMarkdown != "没有找到阅卷记录。" {
		t.Errorf("空记录应该返回 '没有找到阅卷记录。'，但得到了 '%s'", emptyMarkdown)
	}
}

// 辅助函数：检查字符串是否包含子串
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
