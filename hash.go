package main

import (
	"crypto/sha256"
	"encoding/hex"
	"strings"
)

// GenerateImageHash 从base64图片字符串生成SHA-256哈希值
// 返回一个64字符的十六进制字符串
func GenerateImageHash(base64Image string) string {
	// 如果base64字符串包含前缀(如data:image/png;base64,)，则去除前缀
	if idx := strings.Index(base64Image, ","); idx != -1 {
		base64Image = base64Image[idx+1:]
	}

	// 计算SHA-256哈希值
	hash := sha256.Sum256([]byte(base64Image))
	
	// 将哈希值转换为十六进制字符串
	return hex.EncodeToString(hash[:])
}
